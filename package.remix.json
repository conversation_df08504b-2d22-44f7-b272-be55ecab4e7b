{"name": "remix-cloudflare-neon-starter", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix build", "dev": "remix dev --manual", "start": "wrangler pages dev ./build/client", "deploy": "wrangler pages deploy ./build/client", "typecheck": "tsc"}, "dependencies": {"@remix-run/cloudflare": "^2.5.1", "@remix-run/cloudflare-pages": "^2.5.1", "@remix-run/react": "^2.5.1", "@neondatabase/serverless": "^0.9.0", "remix-auth": "^3.6.0", "remix-auth-google-credential": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "isbot": "^4.1.0", "tailwindcss": "^3.4.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@remix-run/dev": "^2.5.1", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "typescript": "^5.1.6", "wrangler": "^3.22.1", "@cloudflare/workers-types": "^4.20240117.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.33"}, "engines": {"node": ">=18.0.0"}}