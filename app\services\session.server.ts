import { createCookieSessionStorage } from "@remix-run/cloudflare";

// Get environment variables with fallbacks
function getSessionSecret(): string {
  // In Cloudflare Workers, use globalThis or fallback
  const secret =
    (typeof process !== "undefined" && process.env?.SESSION_COOKIE_SECRET) ||
    (typeof globalThis !== "undefined" && (globalThis as any).SESSION_COOKIE_SECRET) ||
    "default-secret-change-in-production";

  return secret;
}

function isProduction(): boolean {
  const nodeEnv =
    (typeof process !== "undefined" && process.env?.NODE_ENV) ||
    (typeof globalThis !== "undefined" && (globalThis as any).NODE_ENV) ||
    "development";

  return nodeEnv === "production";
}

// Create session storage
export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "__session",
    httpOnly: true,
    maxAge: 60 * 60 * 24 * 30, // 30 days
    path: "/",
    sameSite: "lax",
    secrets: [getSessionSecret()],
    secure: isProduction(),
  },
});

export const { getSession, commitSession, destroySession } = sessionStorage;
