import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Form, useLoaderData } from "@remix-run/react";
import { requireUser } from "~/lib/auth/middleware.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Console - AI SaaS Starter" },
    { name: "description", content: "Your AI workspace console" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Require authentication
  const user = await requireUser(request);

  return json({
    user,
  });
}

export default function ConsolePage() {
  const { user } = useLoaderData<typeof loader>();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Welcome to your Console
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Hello, {user.name || user.email}!
            </p>
          </div>

          {/* Logout Button */}
          <Form action="/auth/logout" method="post">
            <button
              type="submit"
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Logout
            </button>
          </Form>
        </div>

        {/* User Info Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            User Information
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">{user.email}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Name
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {user.name || "Not provided"}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                User ID
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white font-mono">{user.id}</p>
            </div>
            {user.avatar && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Avatar
                </label>
                <img src={user.avatar} alt="User avatar" className="mt-1 w-12 h-12 rounded-full" />
              </div>
            )}
          </div>
        </div>

        {/* Success Message */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Authentication Successful!</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>
                  Your Remix + Cloudflare + Neon + remix-auth setup is working perfectly! Google One
                  Tap authentication has been successfully implemented.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
