# Remix + Cloudflare + Neon + remix-auth Environment Variables

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Neon Database Configuration
NEON_DATABASE_URL=postgresql://username:<EMAIL>/dbname?sslmode=require

# Session Configuration
SESSION_COOKIE_SECRET=your_super_secret_session_key_here_change_in_production

# Environment
NODE_ENV=development

# Optional: Enable/Disable Features
GOOGLE_ONE_TAP_ENABLED=true

# Instructions:
# 1. Copy this file to .env.local
# 2. Replace the placeholder values with your actual credentials
# 3. Never commit .env.local to version control
# 4. For production, set these as Cloudflare Workers secrets using:
#    wrangler secret put GOOGLE_CLIENT_ID
#    wrangler secret put GOOGLE_CLIENT_SECRET
#    wrangler secret put NEON_DATABASE_URL
#    wrangler secret put SESSION_COOKIE_SECRET
