/**
 * Google One Tap 测试页面
 * 用于调试 Google 认证问题
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData } from "@remix-run/react";
import { useEffect, useState } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Google Auth Test" },
    { name: "description", content: "Test Google One Tap authentication" },
  ];
};

export async function loader({ context }: LoaderFunctionArgs) {
  return json({
    googleClientId: context.cloudflare?.env?.GOOGLE_CLIENT_ID,
    googleClientSecret: context.cloudflare?.env?.GOOGLE_CLIENT_SECRET ? "已配置" : "未配置",
    oneTapEnabled: context.cloudflare?.env?.ONE_TAP_ENABLED,
  });
}

export default function GoogleAuthTest() {
  const { googleClientId, googleClientSecret, oneTapEnabled } = useLoaderData<typeof loader>();
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs((prev) => [...prev, `[${timestamp}] ${message}`]);
    console.log(message);
  };

  useEffect(() => {
    if (!googleClientId) {
      addLog("❌ Google Client ID not found");
      return;
    }

    addLog(`✅ Google Client ID: ${googleClientId}`);
    addLog(`✅ One Tap Enabled: ${oneTapEnabled}`);

    // Load Google One Tap script
    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;

    script.onload = () => {
      addLog("✅ Google GSI script loaded");

      try {
        if (window.google?.accounts?.id) {
          addLog("✅ Google Accounts API available");

          window.google.accounts.id.initialize({
            client_id: googleClientId,
            callback: (response: any) => {
              addLog("✅ Google callback triggered");
              addLog(`Credential length: ${response.credential?.length || 0}`);

              if (response.credential) {
                // Set cookie and redirect
                document.cookie = `g_credential=${response.credential}; Path=/; SameSite=Lax${window.location.protocol === "https:" ? "; Secure" : ""}`;
                addLog("✅ Cookie set, redirecting...");
                window.location.href = "/auth/google/callback";
              }
            },
            auto_select: false,
            cancel_on_tap_outside: true,
            use_fedcm_for_prompt: false,
            // 添加更多调试信息
            context: "signin",
            ux_mode: "popup",
            itp_support: true,
          });

          addLog("✅ Google One Tap initialized");
          setIsLoaded(true);

          // Show prompt
          window.google.accounts.id.prompt((notification: any) => {
            addLog("📋 Prompt notification received");
            addLog(`📋 Notification object: ${JSON.stringify(notification, null, 2)}`);

            if (notification.isNotDisplayed?.()) {
              const reason = notification.getNotDisplayedReason?.();
              addLog(`❌ One Tap not displayed: ${reason}`);

              // 提供更详细的错误解释
              switch (reason) {
                case "unregistered_origin":
                  addLog(
                    `💡 解决方案: 在 Google Console 中添加 ${window.location.origin} 到授权的 JavaScript 来源`
                  );
                  break;
                case "opt_out_or_no_session":
                  addLog("💡 解决方案: 用户之前选择了不显示，请清除浏览器数据或使用无痕模式");
                  break;
                case "invalid_client":
                  addLog("💡 解决方案: Google Client ID 配置错误");
                  break;
                default:
                  addLog(`💡 未知错误原因: ${reason}`);
              }
            } else {
              addLog("✅ One Tap prompt displayed");
            }
          });
        } else {
          addLog("❌ Google Accounts API not available");
        }
      } catch (err) {
        addLog(`❌ Initialization error: ${err}`);
      }
    };

    script.onerror = () => {
      addLog("❌ Failed to load Google GSI script");
    };

    document.head.appendChild(script);

    return () => {
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      );
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, [googleClientId, oneTapEnabled]);

  const handleManualPrompt = () => {
    if (window.google?.accounts?.id) {
      addLog("🔄 Triggering manual prompt...");
      window.google.accounts.id.prompt();
    } else {
      addLog("❌ Google API not available for manual prompt");
    }
  };

  const handleTestOrigin = async () => {
    addLog("🔍 Testing origin validation...");
    addLog(`Current origin: ${window.location.origin}`);
    addLog(`Current protocol: ${window.location.protocol}`);
    addLog(`Current hostname: ${window.location.hostname}`);
    addLog(`Current port: ${window.location.port}`);
    addLog(`User agent: ${navigator.userAgent}`);

    // 检查 Google One Tap 的详细状态
    if (window.google?.accounts?.id) {
      addLog("✅ Google Accounts API 可用");

      // 尝试获取更多调试信息
      try {
        // 检查是否有存储的 Google 状态
        const googleState = localStorage.getItem("google.accounts.id.hint");
        addLog(`Google 本地状态: ${googleState ? "存在" : "不存在"}`);

        // 检查 cookies
        const cookies = document.cookie;
        const googleCookies = cookies
          .split(";")
          .filter(
            (cookie) =>
              cookie.toLowerCase().includes("google") ||
              cookie.toLowerCase().includes("gsi") ||
              cookie.toLowerCase().includes("g_")
          );
        addLog(`Google 相关 cookies: ${googleCookies.length} 个`);
        googleCookies.forEach((cookie) => addLog(`  - ${cookie.trim()}`));
      } catch (error) {
        addLog(`检查本地状态时出错: ${error}`);
      }
    } else {
      addLog("❌ Google Accounts API 不可用");
    }

    // 测试网络连接到 Google
    try {
      addLog("🌐 测试到 Google 的网络连接...");

      // 尝试多种方式测试连接
      const tests = [
        {
          url: "https://accounts.google.com/gsi/client",
          method: "HEAD",
          name: "Google GSI (HEAD)",
        },
        { url: "https://accounts.google.com/gsi/client", method: "GET", name: "Google GSI (GET)" },
        { url: "https://www.google.com", method: "HEAD", name: "Google 主站" },
      ];

      for (const test of tests) {
        try {
          const response = await fetch(test.url, {
            method: test.method,
            mode: "no-cors", // 尝试绕过 CORS
          });
          addLog(`✅ ${test.name}: ${response.status || "no-cors 模式"}`);
        } catch (error) {
          addLog(`❌ ${test.name}: ${error}`);
        }
      }
    } catch (error) {
      addLog(`❌ 网络测试失败: ${error}`);
    }

    // 检查是否在 iframe 中
    if (window.self !== window.top) {
      addLog("⚠️ 应用运行在 iframe 中，这可能影响 Google One Tap");
    } else {
      addLog("✅ 应用运行在顶级窗口中");
    }

    // 检查 HTTPS 状态
    if (window.location.protocol === "https:") {
      addLog("✅ 使用 HTTPS 协议");
    } else {
      addLog("⚠️ 使用 HTTP 协议（开发环境正常）");
    }
  };

  const handleForceReset = () => {
    addLog("🔄 强制重置 Google One Tap...");

    // 清除可能的 Google One Tap 状态
    if (window.google?.accounts?.id) {
      try {
        // 取消当前的提示
        window.google.accounts.id.cancel();
        addLog("✅ 已取消当前提示");
      } catch (error) {
        addLog(`⚠️ 取消提示时出错: ${error}`);
      }

      // 重新初始化
      setTimeout(() => {
        if (window.google?.accounts?.id) {
          addLog("🔄 重新初始化 Google One Tap...");
          window.google.accounts.id.initialize({
            client_id: googleClientId,
            callback: (response: any) => {
              addLog("✅ 重置后的回调触发");
              if (response.credential) {
                document.cookie = `g_credential=${response.credential}; Path=/; SameSite=Lax${window.location.protocol === "https:" ? "; Secure" : ""}`;
                addLog("✅ Cookie 已设置，准备重定向...");
                window.location.href = "/auth/google/callback";
              }
            },
            auto_select: false,
            cancel_on_tap_outside: true,
            use_fedcm_for_prompt: false,
            context: "signin",
            ux_mode: "popup",
            itp_support: true,
          });

          // 立即显示提示
          window.google.accounts.id.prompt((notification: any) => {
            if (notification.isNotDisplayed?.()) {
              const reason = notification.getNotDisplayedReason?.();
              addLog(`❌ 重置后仍然无法显示: ${reason}`);
            } else {
              addLog("✅ 重置后成功显示提示");
            }
          });
        }
      }, 1000);
    }
  };

  const handleForceInit = () => {
    addLog("🔄 强制重新初始化 Google One Tap...");

    // 移除现有脚本
    const existingScript = document.querySelector(
      'script[src="https://accounts.google.com/gsi/client"]'
    );
    if (existingScript) {
      existingScript.remove();
      addLog("🗑️ 已移除现有 Google 脚本");
    }

    // 重新加载脚本
    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;

    script.onload = () => {
      addLog("✅ Google 脚本重新加载成功");

      if (window.google?.accounts?.id) {
        addLog("🔧 重新初始化 Google One Tap...");

        window.google.accounts.id.initialize({
          client_id: googleClientId,
          callback: (response: any) => {
            addLog("✅ 强制初始化后的回调触发");
            if (response.credential) {
              document.cookie = `g_credential=${response.credential}; Path=/; SameSite=Lax${window.location.protocol === "https:" ? "; Secure" : ""}`;
              addLog("✅ Cookie 已设置，准备重定向...");
              window.location.href = "/auth/google/callback";
            }
          },
          auto_select: false,
          cancel_on_tap_outside: true,
          use_fedcm_for_prompt: false,
          context: "signin",
          ux_mode: "popup",
          itp_support: true,
        });

        addLog("✅ 重新初始化完成，尝试显示提示...");

        // 立即尝试显示
        window.google.accounts.id.prompt((notification: any) => {
          addLog(`📋 强制初始化后的提示通知: ${JSON.stringify(notification, null, 2)}`);

          if (notification.isNotDisplayed?.()) {
            const reason = notification.getNotDisplayedReason?.();
            addLog(`❌ 强制初始化后仍无法显示: ${reason}`);
          } else {
            addLog("✅ 强制初始化后成功显示提示！");
          }
        });
      }
    };

    script.onerror = () => {
      addLog("❌ Google 脚本重新加载失败");
    };

    document.head.appendChild(script);
  };

  const handleCheckGoogleConfig = async () => {
    addLog("🔧 检查 Google Console 配置...");

    // 尝试直接验证 client_id
    try {
      const response = await fetch(
        `https://oauth2.googleapis.com/tokeninfo?client_id=${googleClientId}`
      );
      const data = await response.text();

      if (response.status === 400) {
        addLog("✅ Client ID 格式正确（400 错误是正常的，因为缺少 token）");
      } else {
        addLog(`⚠️ 意外的响应状态: ${response.status}`);
        addLog(`响应内容: ${data}`);
      }
    } catch (error) {
      addLog(`❌ 验证 Client ID 时出错: ${error}`);
    }

    // 检查当前时间和配置时间
    const now = new Date();
    addLog(`当前时间: ${now.toLocaleString()}`);
    addLog("Google Console 显示创建时间: 2025年6月22日 GMT+8 13:26:35");
    addLog("Google Console 显示最后使用: 2025年6月21日");

    const configTime = new Date("2025-06-22T13:26:35+08:00");
    const timeDiff = now.getTime() - configTime.getTime();
    const hoursDiff = Math.floor(timeDiff / (1000 * 60 * 60));
    const minutesDiff = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    addLog(`配置已过去: ${hoursDiff} 小时 ${minutesDiff} 分钟`);

    if (hoursDiff < 1) {
      addLog("⚠️ 配置时间较短，Google 可能还未完全生效");
      addLog("💡 建议: 等待 1-2 小时后再试，或尝试无痕模式");
    } else {
      addLog("✅ 配置时间充足，应该已经生效");
    }

    // 提供具体的解决建议
    addLog("🔧 建议的解决步骤:");
    addLog("1. 使用无痕模式测试");
    addLog("2. 清除浏览器所有数据");
    addLog("3. 尝试不同的浏览器");
    addLog("4. 检查是否有防火墙或代理阻止");
    addLog("5. 等待更长时间（最多 24 小时）");
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Google One Tap 测试</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 配置信息 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">配置信息</h2>
            <div className="space-y-2 text-sm">
              <div>
                <strong>Google Client ID:</strong>
                <span className={googleClientId ? "text-green-600" : "text-red-600"}>
                  {googleClientId || "未配置"}
                </span>
              </div>
              <div>
                <strong>Client ID 长度:</strong>
                <span className="text-blue-600">
                  {googleClientId ? googleClientId.length : 0} 字符
                </span>
              </div>
              <div>
                <strong>Client ID 格式:</strong>
                <span
                  className={
                    googleClientId?.endsWith(".apps.googleusercontent.com")
                      ? "text-green-600"
                      : "text-red-600"
                  }
                >
                  {googleClientId?.endsWith(".apps.googleusercontent.com") ? "✅ 正确" : "❌ 错误"}
                </span>
              </div>
              <div>
                <strong>Client Secret:</strong>
                <span
                  className={googleClientSecret === "已配置" ? "text-green-600" : "text-red-600"}
                >
                  {googleClientSecret}
                </span>
              </div>
              <div>
                <strong>One Tap Enabled:</strong>
                <span className={oneTapEnabled === "true" ? "text-green-600" : "text-red-600"}>
                  {oneTapEnabled || "false"}
                </span>
              </div>
              <div>
                <strong>当前域名:</strong>{" "}
                {typeof window !== "undefined" ? window.location.origin : "SSR"}
              </div>
              <div>
                <strong>协议:</strong>{" "}
                {typeof window !== "undefined" ? window.location.protocol : "SSR"}
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">操作</h2>
            <div className="space-y-4">
              <button
                onClick={handleManualPrompt}
                disabled={!isLoaded}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
              >
                手动触发 Google One Tap
              </button>

              <button
                onClick={handleTestOrigin}
                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                测试域名和连接
              </button>

              <button
                onClick={handleForceReset}
                className="w-full px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
              >
                强制重置 One Tap
              </button>

              <button
                onClick={handleForceInit}
                className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                强制重新初始化
              </button>

              <button
                onClick={handleCheckGoogleConfig}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                检查 Google 配置
              </button>

              <button
                onClick={() => (window.location.href = "/auth/google/oauth")}
                className="w-full px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
              >
                尝试传统 OAuth 登录
              </button>

              <button
                onClick={() => setLogs([])}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                清除日志
              </button>
            </div>
          </div>
        </div>

        {/* 日志输出 */}
        <div className="mt-8 bg-black text-green-400 p-6 rounded-lg font-mono text-sm">
          <h2 className="text-xl font-semibold mb-4 text-white">调试日志</h2>
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="text-gray-500">等待日志输出...</div>
            ) : (
              logs.map((log, index) => <div key={index}>{log}</div>)
            )}
          </div>
        </div>

        {/* 帮助信息 */}
        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">常见问题解决方案</h2>
          <div className="space-y-4 text-sm">
            <div>
              <strong>1. "One Tap not displayed: opt_out_or_no_session"</strong>
              <p>用户之前选择了不显示 One Tap，需要清除浏览器数据或使用无痕模式</p>
            </div>
            <div>
              <strong>2. "One Tap not displayed: invalid_client"</strong>
              <p>Google Client ID 配置错误或域名未在 Google Console 中授权</p>
            </div>
            <div>
              <strong>3. "One Tap not displayed: unregistered_origin"</strong>
              <p>当前域名未在 Google Console 的授权 JavaScript 来源中添加</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
