# Deployment Guide - Remix + Cloudflare + Neon + remix-auth

## Prerequisites

1. **Cloudflare Account** with Workers enabled
2. **Google Cloud Console** project with OAuth 2.0 configured
3. **Neon Database** instance
4. **Node.js 18+** and **pnpm** installed

## Environment Setup

### 1. Google OAuth Configuration

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized origins:
   - `http://localhost:8788` (for development)
   - `https://your-domain.workers.dev` (for production)
7. Copy Client ID and Client Secret

### 2. Neon Database Setup

1. Create account at [Neon](https://neon.tech/)
2. Create a new database
3. Copy the connection string
4. Run the migration SQL:

```sql
-- Run this in Neon SQL Editor
CREATE TABLE users (
  id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email        TEXT UNIQUE NOT NULL,
  name         TEXT,
  avatar       TEXT,
  email_verified BOOLEAN DEFAULT FALSE,
  last_login_at TIMESTAMPTZ,
  created_at   TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE accounts (
  id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id          UUID REFERENCES users(id) ON DELETE CASCADE,
  provider         TEXT NOT NULL,
  provider_user_id TEXT NOT NULL,
  created_at       TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(provider, provider_user_id)
);

CREATE TABLE sessions (
  id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id      UUID REFERENCES users(id) ON DELETE CASCADE,
  expires_at   TIMESTAMPTZ NOT NULL,
  created_at   TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_accounts_user_id ON accounts (user_id);
CREATE INDEX idx_sessions_user_id ON sessions (user_id);
CREATE INDEX idx_sessions_expires_at ON sessions (expires_at);
```

### 3. Local Development

1. Copy environment variables:
```bash
cp .env.example .env.local
```

2. Fill in your credentials in `.env.local`

3. Install dependencies:
```bash
pnpm install
```

4. Start development server:
```bash
pnpm dev
```

### 4. Production Deployment

1. Set Cloudflare Workers secrets:
```bash
wrangler secret put GOOGLE_CLIENT_ID
wrangler secret put GOOGLE_CLIENT_SECRET
wrangler secret put NEON_DATABASE_URL
wrangler secret put SESSION_COOKIE_SECRET
```

2. Deploy to Cloudflare Workers:
```bash
pnpm run deploy
```

## Testing Authentication

1. Visit `/auth/login`
2. Google One Tap should appear automatically
3. Click "Continue with Google" if One Tap doesn't work
4. After successful login, you'll be redirected to `/console`
5. Test logout functionality

## Troubleshooting

### Google One Tap not appearing
- Check browser console for errors
- Verify Google Client ID is correct
- Ensure domain is added to Google OAuth origins
- Try incognito mode

### Database connection issues
- Verify Neon connection string format
- Check if database tables exist
- Ensure Neon database is not paused

### Session issues
- Verify SESSION_COOKIE_SECRET is set
- Check browser cookies
- Clear browser data and try again

## Security Notes

- Never expose Google Client Secret in client-side code
- Use strong SESSION_COOKIE_SECRET (32+ characters)
- Enable HTTPS in production
- Regularly rotate secrets
- Monitor authentication logs
